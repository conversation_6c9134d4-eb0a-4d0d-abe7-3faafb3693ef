<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectTypeGuids>{82b43b9b-a64c-4715-b499-d71e9ca2bd60};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <ProjectGuid>{A662461D-7FDC-4083-8F8F-5FE5E5300588}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AICodeAssistant</RootNamespace>
    <AssemblyName>AICodeAssistant</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <GeneratePkgDefFile>true</GeneratePkgDefFile>
    <UseCodebase>true</UseCodebase>
    <IncludeAssemblyInVSIXContainer>true</IncludeAssemblyInVSIXContainer>
    <IncludeDebugSymbolsInVSIXContainer>true</IncludeDebugSymbolsInVSIXContainer>
    <IncludeDebugSymbolsInLocalVSIXDeployment>false</IncludeDebugSymbolsInLocalVSIXDeployment>
    <CopyBuildOutputToOutputDirectory>true</CopyBuildOutputToOutputDirectory>
    <CopyOutputSymbolsToOutputDirectory>true</CopyOutputSymbolsToOutputDirectory>
    <StartAction>Program</StartAction>
    <StartProgram Condition="'$(DevEnvDir)' != ''">$(DevEnvDir)devenv.exe</StartProgram>
    <StartArguments>/rootsuffix Exp</StartArguments>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Commands\ChatToolWindowCommand.cs" />
    <Compile Include="Commands\SettingsCommand.cs" />
    <Compile Include="AICodeAssistantPackage.cs" />
    <Compile Include="Constants.cs" />
    <Compile Include="Services\ILlmProvider.cs" />
    <Compile Include="Services\OpenAiProvider.cs" />
    <Compile Include="Services\OllamaProvider.cs" />
    <Compile Include="Services\OllamaModelCache.cs" />
    <Compile Include="Services\LlmProviderFactory.cs" />
    <Compile Include="Services\ContextExtractor.cs" />
    <Compile Include="Services\SensitiveDataMasker.cs" />
    <Compile Include="Services\SettingsManager.cs" />
    <Compile Include="Resources\PromptTemplates.cs" />
    <Compile Include="Features\Completion\AICompletionSource.cs" />
    <Compile Include="Features\Completion\AICompletionSourceProvider.cs" />
    <Compile Include="Features\Completion\PureGhostTextProvider.cs" />
    <Compile Include="Features\Completion\GhostTextCompletionProvider.cs" />
    <Compile Include="Features\Completion\GhostTextSession.cs" />
    <Compile Include="Features\Completion\GhostTextController.cs" />
    <Compile Include="Features\Completion\DebugGhostTextProvider.cs" />
    <Compile Include="Features\Completion\AIInlineCompletionProvider.cs" />
    <Compile Include="Features\Completion\AIInlineCompletionSession.cs" />
    <Compile Include="Features\Completion\InlineCompletionController.cs" />
    <Compile Include="Features\Completion\InlineSuggestionHandler.cs" />
    <Compile Include="Features\Completion\SimpleTestProvider.cs" />
    <Compile Include="Features\Completion\StandardInlineCompletionProvider.cs" />
    <Compile Include="Features\SideChat\ChatMessage.cs" />
    <Compile Include="UI\AppSettings.cs" />
    <Compile Include="UI\SettingsPage.xaml.cs">
      <DependentUpon>SettingsPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="source.extension.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>source.extension.vsixmanifest</DependentUpon>
    </Compile>
    <Compile Include="VSCommandTable.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>VSCommandTable.vsct</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="source.extension.vsixmanifest">
      <SubType>Designer</SubType>
      <Generator>VsixManifestGenerator</Generator>
      <LastGenOutput>source.extension.cs</LastGenOutput>
    </None>
    <Content Include="Resources\Icon.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <IncludeInVSIX>true</IncludeInVSIX>
    </Content>
    <Content Include="README.md">
      <IncludeInVSIX>true</IncludeInVSIX>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <VSCTCompile Include="VSCommandTable.vsct">
      <ResourceName>Menus.ctmenu</ResourceName>
      <Generator>VsctGenerator</Generator>
      <LastGenOutput>VSCommandTable.cs</LastGenOutput>
    </VSCTCompile>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ToolWindows\MyToolWindow.cs" />
    <Page Include="ToolWindows\MyToolWindowControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Compile Include="ToolWindows\MyToolWindowControl.xaml.cs">
      <DependentUpon>MyToolWindowControl.xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Page Include="UI\SettingsPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Design" />
    <Reference Include="System.Xaml" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsBase" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Community.VisualStudio.VSCT" Version="16.0.29.6" PrivateAssets="all" />
    <PackageReference Include="Community.VisualStudio.Toolkit.17" Version="17.0.533" ExcludeAssets="Runtime">
      <IncludeAssets>compile; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VSSDK.BuildTools" Version="17.14.2094">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
    <PackageReference Include="Microsoft.VisualStudio.Language.Intellisense" Version="17.14.249" />
    <PackageReference Include="Microsoft.VisualStudio.Text.UI.Wpf" Version="17.14.249" />
    <PackageReference Include="Microsoft.VisualStudio.Text.UI" Version="17.14.249" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="Markdig" Version="0.41.2" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\VSSDK\Microsoft.VsSDK.targets" Condition="'$(VSToolsPath)' != ''" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>