using System;
using System.ComponentModel.Composition;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// Ghost Text 诊断工具 - 用于检查Ghost Text功能是否正常工作
    /// </summary>
    [Export(typeof(IWpfTextViewCreationListener))]
    [ContentType("any")]
    [TextViewRole(PredefinedTextViewRoles.Document)]
    public class GhostTextDiagnostics : IWpfTextViewCreationListener
    {
        public void TextViewCreated(IWpfTextView textView)
        {
            try
            {
                ActivityLog.LogInformation("GhostTextDiagnostics", "=== GHOST TEXT 诊断开始 ===");
                
                // 检查文本视图
                ActivityLog.LogInformation("GhostTextDiagnostics", $"文本视图创建: {textView != null}");
                ActivityLog.LogInformation("GhostTextDiagnostics", $"文本视图类型: {textView?.GetType().Name}");
                
                // 检查装饰层
                var ghostLayer = textView?.GetAdornmentLayer("GhostTextLayer");
                ActivityLog.LogInformation("GhostTextDiagnostics", $"GhostTextLayer 可用: {ghostLayer != null}");
                
                var simpleLayer = textView?.GetAdornmentLayer("SimpleTestLayer");
                ActivityLog.LogInformation("GhostTextDiagnostics", $"SimpleTestLayer 可用: {simpleLayer != null}");
                
                var debugLayer = textView?.GetAdornmentLayer("DebugGhostTextLayer");
                ActivityLog.LogInformation("GhostTextDiagnostics", $"DebugGhostTextLayer 可用: {debugLayer != null}");
                
                // 检查现有属性
                if (textView?.Properties != null)
                {
                    var hasGhostManager = textView.Properties.ContainsProperty(typeof(PureGhostTextManager));
                    ActivityLog.LogInformation("GhostTextDiagnostics", $"PureGhostTextManager 已注册: {hasGhostManager}");
                    
                    var hasSimpleManager = textView.Properties.ContainsProperty(typeof(SimpleTestManager));
                    ActivityLog.LogInformation("GhostTextDiagnostics", $"SimpleTestManager 已注册: {hasSimpleManager}");
                }
                
                // 检查内容类型
                if (textView?.TextBuffer?.ContentType != null)
                {
                    var contentType = textView.TextBuffer.ContentType.TypeName;
                    ActivityLog.LogInformation("GhostTextDiagnostics", $"内容类型: {contentType}");
                }
                
                // 检查文档路径
                if (textView?.TextBuffer?.Properties?.TryGetProperty(typeof(Microsoft.VisualStudio.Text.ITextDocument), out Microsoft.VisualStudio.Text.ITextDocument document) == true)
                {
                    ActivityLog.LogInformation("GhostTextDiagnostics", $"文档路径: {document.FilePath ?? "Unknown"}");
                }
                
                ActivityLog.LogInformation("GhostTextDiagnostics", "=== GHOST TEXT 诊断结束 ===");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("GhostTextDiagnostics", $"诊断失败: {ex.Message}");
                ActivityLog.LogError("GhostTextDiagnostics", $"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}
