using System;
using System.ComponentModel.Composition;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Text.Formatting;
using Microsoft.VisualStudio.Text.Tagging;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.Services;
using AICodeAssistant.UI;
using AICodeAssistant.Resources;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// Adornment layer definition for Ghost Text
    /// </summary>
    internal static class GhostTextLayerDefinitions
    {
        [Export(typeof(AdornmentLayerDefinition))]
        [Name("GhostTextLayer")]
        [Order(After = PredefinedAdornmentLayers.Selection, Before = PredefinedAdornmentLayers.Text)]
        internal static AdornmentLayerDefinition GhostTextLayerDefinition;
    }

    /// <summary>
    /// Pure Ghost Text implementation for Visual Studio
    /// Creates inline AI suggestions without dropdown completion interference
    /// </summary>
    [Export(typeof(IWpfTextViewCreationListener))]
    [ContentType("CSharp")]
    [ContentType("code")]
    [ContentType("text")]
    [ContentType("any")]
    [TextViewRole(PredefinedTextViewRoles.Editable)]
    [TextViewRole(PredefinedTextViewRoles.Document)]
    [TextViewRole(PredefinedTextViewRoles.Interactive)]
    public class PureGhostTextProvider : IWpfTextViewCreationListener
    {
        public void TextViewCreated(IWpfTextView textView)
        {
            try
            {
                ActivityLog.LogInformation("PureGhostTextProvider", "Creating Ghost Text manager for text view");
                
                // Create a Ghost Text manager for this text view
                var manager = new PureGhostTextManager(textView);
                textView.Properties.AddProperty(typeof(PureGhostTextManager), manager);
                
                ActivityLog.LogInformation("PureGhostTextProvider", "Ghost Text manager created successfully");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextProvider", $"Failed to create Ghost Text manager: {ex.Message}");
                ActivityLog.LogError("PureGhostTextProvider", $"Stack trace: {ex.StackTrace}");
            }
        }
    }

    /// <summary>
    /// Pure Ghost Text Manager - handles all Ghost Text functionality
    /// </summary>
    public class PureGhostTextManager : IDisposable
    {
        private readonly IWpfTextView _textView;
        private readonly IAdornmentLayer _adornmentLayer;
        
        private Timer _triggerTimer;
        private CancellationTokenSource _currentRequest;
        private string _currentGhostText;
        private SnapshotPoint? _ghostTextPosition;
        
        // Configuration
        private const int TriggerDelayMs = 500; // 0.5 seconds - faster for testing
        private const int MinLineLength = 1; // More lenient
        private const string AdornmentTag = "GhostTextSuggestion";
        
        // Services
        private ILlmProvider _llmProvider;
        private CompletionSettings _settings;

        public PureGhostTextManager(IWpfTextView textView)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            
            try
            {
                // Get the adornment layer
                _adornmentLayer = _textView.GetAdornmentLayer("GhostTextLayer");
                if (_adornmentLayer == null)
                {
                    ActivityLog.LogError("PureGhostTextManager", "Failed to get GhostTextLayer adornment layer");
                    return;
                }

                // Initialize services
                InitializeServices();

                // Subscribe to events
                _textView.TextBuffer.Changed += OnTextChanged;
                _textView.Caret.PositionChanged += OnCaretPositionChanged;
                _textView.Closed += OnClosed;

                // Subscribe to keyboard events through the visual element
                if (_textView.VisualElement != null)
                {
                    _textView.VisualElement.KeyDown += OnKeyDown;
                    _textView.VisualElement.LostFocus += OnLostFocus;
                }

                ActivityLog.LogInformation("PureGhostTextManager", "Ghost Text manager initialized successfully");
                ActivityLog.LogInformation("PureGhostTextManager", $"Adornment layer available: {_adornmentLayer != null}");
                ActivityLog.LogInformation("PureGhostTextManager", $"LLM provider available: {_llmProvider?.IsAvailable}");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"Initialization failed: {ex.Message}");
                ActivityLog.LogError("PureGhostTextManager", $"Stack trace: {ex.StackTrace}");
            }
        }

        private void InitializeServices()
        {
            try
            {
                // Load settings
                var appSettings = SettingsManager.LoadSettings() ?? AppSettings.CreateDefault();
                _settings = appSettings.Completion;

                ActivityLog.LogInformation("PureGhostTextManager", $"Settings loaded - Enabled: {_settings.Enabled}");

                if (!_settings.Enabled)
                {
                    ActivityLog.LogInformation("PureGhostTextManager", "Ghost Text is disabled in settings");
                    return;
                }

                // Create LLM provider
                var options = CreateLlmOptions(appSettings);
                _llmProvider = LlmProviderFactory.CreateProvider(appSettings.ProviderType, options);

                ActivityLog.LogInformation("PureGhostTextManager", $"LLM provider created: {_llmProvider?.Name}, Available: {_llmProvider?.IsAvailable}");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"Service initialization failed: {ex.Message}");
            }
        }

        private LlmOptions CreateLlmOptions(AppSettings settings)
        {
            return settings.ProviderType switch
            {
                LlmProviderType.OpenAI => new LlmOptions
                {
                    ApiKey = settings.OpenAI.ApiKey,
                    ApiBase = settings.OpenAI.ApiBase,
                    Model = settings.OpenAI.Model,
                    Temperature = 0.1,
                    MaxTokens = 64,
                    TimeoutSeconds = 10
                },
                LlmProviderType.Ollama => new LlmOptions
                {
                    ApiBase = settings.Ollama.ApiBase,
                    Model = settings.Ollama.Model,
                    Temperature = 0.1,
                    MaxTokens = 64,
                    TimeoutSeconds = 15
                },
                _ => LlmProviderFactory.GetDefaultOptions(LlmProviderType.Ollama)
            };
        }

        private void OnTextChanged(object sender, TextContentChangedEventArgs e)
        {
            try
            {
                ActivityLog.LogInformation("PureGhostTextManager", "Text changed - clearing Ghost Text and scheduling new trigger");
                
                // Clear current Ghost Text
                ClearGhostText();

                // Check if this is a user insertion
                if (e.Changes.Count > 0)
                {
                    var change = e.Changes[0];
                    if (!string.IsNullOrEmpty(change.NewText) && change.OldText.Length == 0)
                    {
                        ActivityLog.LogInformation("PureGhostTextManager", $"User inserted text: '{change.NewText}' - scheduling Ghost Text trigger");
                        ScheduleGhostTextTrigger();
                    }
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"OnTextChanged failed: {ex.Message}");
            }
        }

        private void OnCaretPositionChanged(object sender, CaretPositionChangedEventArgs e)
        {
            try
            {
                // Clear Ghost Text if caret moved away from the suggestion position
                if (_ghostTextPosition.HasValue && 
                    e.NewPosition.BufferPosition != _ghostTextPosition.Value)
                {
                    ActivityLog.LogInformation("PureGhostTextManager", "Caret moved - clearing Ghost Text");
                    ClearGhostText();
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"OnCaretPositionChanged failed: {ex.Message}");
            }
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(_currentGhostText))
                    return;

                ActivityLog.LogInformation("PureGhostTextManager", $"Key pressed: {e.Key} with Ghost Text active");

                switch (e.Key)
                {
                    case Key.Tab:
                        ActivityLog.LogInformation("PureGhostTextManager", "Tab pressed - accepting Ghost Text");
                        AcceptGhostText();
                        e.Handled = true;
                        break;

                    case Key.Escape:
                        ActivityLog.LogInformation("PureGhostTextManager", "Escape pressed - dismissing Ghost Text");
                        ClearGhostText();
                        e.Handled = true;
                        break;

                    case Key.Left:
                    case Key.Right:
                    case Key.Up:
                    case Key.Down:
                    case Key.Home:
                    case Key.End:
                        ActivityLog.LogInformation("PureGhostTextManager", "Navigation key pressed - clearing Ghost Text");
                        ClearGhostText();
                        break;

                    default:
                        // For other keys, clear Ghost Text
                        if (char.IsLetterOrDigit((char)KeyInterop.VirtualKeyFromKey(e.Key)) ||
                            e.Key == Key.Space || e.Key == Key.Enter)
                        {
                            ActivityLog.LogInformation("PureGhostTextManager", "Text input key pressed - clearing Ghost Text");
                            ClearGhostText();
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"OnKeyDown failed: {ex.Message}");
            }
        }

        private void OnLostFocus(object sender, EventArgs e)
        {
            ActivityLog.LogInformation("PureGhostTextManager", "Text view lost focus - clearing Ghost Text");
            ClearGhostText();
        }

        private void OnClosed(object sender, EventArgs e)
        {
            ActivityLog.LogInformation("PureGhostTextManager", "Text view closed - disposing manager");
            Dispose();
        }

        private void ScheduleGhostTextTrigger()
        {
            try
            {
                // Cancel previous timer
                _triggerTimer?.Dispose();

                // Create new timer
                _triggerTimer = new Timer(async _ =>
                {
                    try
                    {
                        await TriggerGhostTextAsync();
                    }
                    catch (Exception ex)
                    {
                        ActivityLog.LogError("PureGhostTextManager", $"Ghost Text trigger failed: {ex.Message}");
                    }
                }, null, TriggerDelayMs, Timeout.Infinite);

                ActivityLog.LogInformation("PureGhostTextManager", $"Ghost Text trigger scheduled for {TriggerDelayMs}ms");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"ScheduleGhostTextTrigger failed: {ex.Message}");
            }
        }

        private async Task TriggerGhostTextAsync()
        {
            try
            {
                ActivityLog.LogInformation("PureGhostTextManager", "Ghost Text trigger fired - checking conditions");

                // 先切换到UI线程获取光标位置
                await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                var caretPosition = _textView.Caret.Position.BufferPosition;

                if (!ShouldShowGhostText(caretPosition))
                {
                    ActivityLog.LogInformation("PureGhostTextManager", "Conditions not met for Ghost Text");
                    return;
                }

                // 如果没有LLM提供者，显示测试文本
                if (_llmProvider == null || !_llmProvider.IsAvailable)
                {
                    ActivityLog.LogWarning("PureGhostTextManager", "LLM provider not available - showing test text");
                    ShowGhostText("TEST_SUGGESTION", caretPosition);
                    return;
                }

                if (!_settings.Enabled)
                {
                    ActivityLog.LogWarning("PureGhostTextManager", "Ghost Text disabled in settings - showing test text");
                    ShowGhostText("DISABLED_TEST", caretPosition);
                    return;
                }

                // Cancel previous request
                _currentRequest?.Cancel();
                _currentRequest = new CancellationTokenSource();

                ActivityLog.LogInformation("PureGhostTextManager", "Generating Ghost Text suggestion...");

                // Generate suggestion
                var suggestion = await GenerateGhostTextAsync(caretPosition, _currentRequest.Token);

                if (!string.IsNullOrEmpty(suggestion) && !_currentRequest.Token.IsCancellationRequested)
                {
                    // Already on UI thread
                    ShowGhostText(suggestion, caretPosition);
                }
                else
                {
                    ActivityLog.LogInformation("PureGhostTextManager", "No suggestion generated - showing fallback");
                    ShowGhostText("NO_SUGGESTION", caretPosition);
                }
            }
            catch (OperationCanceledException)
            {
                ActivityLog.LogInformation("PureGhostTextManager", "Ghost Text generation cancelled");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"TriggerGhostTextAsync failed: {ex.Message}");
                ActivityLog.LogError("PureGhostTextManager", $"Stack trace: {ex.StackTrace}");

                // 显示错误测试文本
                try
                {
                    await ThreadHelper.JoinableTaskFactory.SwitchToMainThreadAsync();
                    var caretPosition = _textView.Caret.Position.BufferPosition;
                    ShowGhostText("ERROR_TEST", caretPosition);
                }
                catch { }
            }
        }

        private bool ShouldShowGhostText(SnapshotPoint position)
        {
            try
            {
                // Get current line
                var line = position.GetContainingLine();
                var lineText = line.GetText();
                var positionInLine = position.Position - line.Start.Position;

                ActivityLog.LogInformation("PureGhostTextManager", $"Checking Ghost Text conditions:");
                ActivityLog.LogInformation("PureGhostTextManager", $"  Line text: '{lineText}'");
                ActivityLog.LogInformation("PureGhostTextManager", $"  Position in line: {positionInLine}");
                ActivityLog.LogInformation("PureGhostTextManager", $"  Line length: {lineText.Length}");

                // 简化条件：只要行不为空就显示
                var trimmedLine = lineText.Trim();
                if (trimmedLine.Length == 0)
                {
                    ActivityLog.LogInformation("PureGhostTextManager", "Empty line - skipping");
                    return false;
                }

                ActivityLog.LogInformation("PureGhostTextManager", "Conditions met - will show Ghost Text");
                return true;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"ShouldShowGhostText failed: {ex.Message}");
                return false;
            }
        }

        private string GetFilePath()
        {
            try
            {
                if (_textView.TextBuffer.Properties.TryGetProperty(typeof(ITextDocument), out ITextDocument document))
                {
                    return document.FilePath ?? "Unknown";
                }
            }
            catch { }
            return "Unknown";
        }

        private bool IsSupportedFileType(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || filePath == "Unknown")
                return true;

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            var supported = Constants.FileExtensions.SupportedCodeFiles.Contains(extension ?? string.Empty);
            
            ActivityLog.LogInformation("PureGhostTextManager", $"File extension: {extension}, Supported: {supported}");
            return supported;
        }

        private async Task<string> GenerateGhostTextAsync(SnapshotPoint position, CancellationToken cancellationToken)
        {
            try
            {
                ActivityLog.LogInformation("PureGhostTextManager", "Extracting context for Ghost Text generation");

                // Extract context
                var context = await ContextExtractor.ExtractContextAsync(_textView, Math.Min(_settings.ContextLines, 15));
                if (context == null)
                {
                    ActivityLog.LogWarning("PureGhostTextManager", "Failed to extract context");
                    return null;
                }

                ActivityLog.LogInformation("PureGhostTextManager", $"Context extracted - Before cursor: {context.BeforeCaret?.Length ?? 0} chars");

                // Build prompt
                var prompt = BuildGhostTextPrompt(context);
                ActivityLog.LogInformation("PureGhostTextManager", $"Prompt built - Length: {prompt.Length}");

                // Call AI
                var response = await _llmProvider.SendAsync(prompt, false, null, cancellationToken);
                if (string.IsNullOrWhiteSpace(response))
                {
                    ActivityLog.LogInformation("PureGhostTextManager", "Empty response from AI");
                    return null;
                }

                ActivityLog.LogInformation("PureGhostTextManager", $"AI response received: '{response}'");

                // Clean response
                var cleaned = CleanGhostTextResponse(response);
                ActivityLog.LogInformation("PureGhostTextManager", $"Cleaned response: '{cleaned}'");

                return cleaned;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"GenerateGhostTextAsync failed: {ex.Message}");
                return null;
            }
        }

        private string BuildGhostTextPrompt(CodeContext context)
        {
            var language = GetLanguageFromFilePath(context.FilePath);

            var systemPrompt = $@"You are a {language} code completion assistant. Complete the code at cursor position.

CRITICAL RULES:
1. Return ONLY the code to insert, no explanations
2. Keep it very short (1-5 words maximum)
3. Complete the current statement/expression
4. Ensure syntactic correctness
5. Don't repeat existing code

Language: {language}";

            var userPrompt = $@"Complete this {language} code at cursor position (▮):

{context.BeforeCaret}▮{context.AfterCaret}

Insert at ▮:";

            return $"{systemPrompt}\n\n{userPrompt}";
        }

        private string CleanGhostTextResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return string.Empty;

            try
            {
                // Remove code block markers
                response = System.Text.RegularExpressions.Regex.Replace(response, @"```[\w]*\n?", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                response = System.Text.RegularExpressions.Regex.Replace(response, @"```", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                // Remove common prefixes
                var prefixes = new[] { "Here's the completion:", "The completion is:", "Complete:", "Insert:", "Add:", "Code:", "Result:" };
                foreach (var prefix in prefixes)
                {
                    if (response.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                    {
                        response = response.Substring(prefix.Length).TrimStart();
                        break;
                    }
                }

                // Take only the first line
                var lines = response.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                if (lines.Length > 0)
                {
                    response = lines[0].Trim();
                }

                // Remove quotes if the entire response is quoted
                if (response.StartsWith("\"") && response.EndsWith("\""))
                {
                    response = response.Substring(1, response.Length - 2);
                }

                // Limit length
                if (response.Length > 50)
                {
                    response = response.Substring(0, 50);
                }

                return response.Trim();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"CleanGhostTextResponse failed: {ex.Message}");
                return response?.Trim() ?? string.Empty;
            }
        }

        private string GetLanguageFromFilePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "code";

            var extension = System.IO.Path.GetExtension(filePath)?.ToLowerInvariant();
            return extension switch
            {
                ".cs" => "C#",
                ".js" => "JavaScript",
                ".ts" => "TypeScript",
                ".py" => "Python",
                ".java" => "Java",
                _ => "code"
            };
        }

        private void ShowGhostText(string suggestion, SnapshotPoint position)
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                ActivityLog.LogInformation("PureGhostTextManager", $"Showing Ghost Text: '{suggestion}' at position {position.Position}");

                // Clear any existing Ghost Text
                ClearGhostText();

                // Store current suggestion
                _currentGhostText = suggestion;
                _ghostTextPosition = position;

                // Create the text block for Ghost Text - make it more visible for testing
                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = suggestion,
                    Foreground = new SolidColorBrush(Colors.Red), // 使用红色更明显
                    Background = new SolidColorBrush(Colors.Yellow), // 添加黄色背景
                    Opacity = 0.9, // 提高透明度
                    FontFamily = _textView.FormattedLineSource.DefaultTextProperties.Typeface.FontFamily,
                    FontSize = _textView.FormattedLineSource.DefaultTextProperties.FontRenderingEmSize + 2, // 稍大字体
                    FontWeight = System.Windows.FontWeights.Bold, // 粗体
                    FontStyle = System.Windows.FontStyles.Italic,
                    Padding = new System.Windows.Thickness(2),
                    ToolTip = "Ghost Text - Press Tab to accept, Esc to dismiss"
                };

                // Create span for the adornment
                var span = new SnapshotSpan(position, 0);

                // Add to adornment layer
                _adornmentLayer.AddAdornment(
                    AdornmentPositioningBehavior.TextRelative,
                    span,
                    AdornmentTag,
                    textBlock,
                    null);

                ActivityLog.LogInformation("PureGhostTextManager", "Ghost Text adornment added to layer");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"ShowGhostText failed: {ex.Message}");
                ActivityLog.LogError("PureGhostTextManager", $"Stack trace: {ex.StackTrace}");
            }
        }

        private void AcceptGhostText()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (string.IsNullOrEmpty(_currentGhostText) || !_ghostTextPosition.HasValue)
                {
                    ActivityLog.LogWarning("PureGhostTextManager", "No Ghost Text to accept");
                    return;
                }

                ActivityLog.LogInformation("PureGhostTextManager", $"Accepting Ghost Text: '{_currentGhostText}'");

                // Insert the Ghost Text
                var edit = _textView.TextBuffer.CreateEdit();
                edit.Insert(_ghostTextPosition.Value.Position, _currentGhostText);
                edit.Apply();

                ActivityLog.LogInformation("PureGhostTextManager", "Ghost Text inserted successfully");

                // Clear Ghost Text
                ClearGhostText();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"AcceptGhostText failed: {ex.Message}");
            }
        }

        private void ClearGhostText()
        {
            try
            {
                if (!string.IsNullOrEmpty(_currentGhostText))
                {
                    ThreadHelper.ThrowIfNotOnUIThread();

                    ActivityLog.LogInformation("PureGhostTextManager", "Clearing Ghost Text");

                    _adornmentLayer.RemoveAllAdornments();
                    _currentGhostText = null;
                    _ghostTextPosition = null;
                }
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"ClearGhostText failed: {ex.Message}");
            }
        }

        public void Dispose()
        {
            try
            {
                ActivityLog.LogInformation("PureGhostTextManager", "Disposing Ghost Text manager");

                // Cancel current request
                _currentRequest?.Cancel();
                _currentRequest?.Dispose();

                // Stop timer
                _triggerTimer?.Dispose();

                // Clear Ghost Text
                ClearGhostText();

                // Unsubscribe from events
                if (_textView != null)
                {
                    _textView.TextBuffer.Changed -= OnTextChanged;
                    _textView.Caret.PositionChanged -= OnCaretPositionChanged;
                    _textView.Closed -= OnClosed;

                    // Unsubscribe from keyboard events
                    if (_textView.VisualElement != null)
                    {
                        _textView.VisualElement.KeyDown -= OnKeyDown;
                        _textView.VisualElement.LostFocus -= OnLostFocus;
                    }
                }

                ActivityLog.LogInformation("PureGhostTextManager", "Ghost Text manager disposed");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("PureGhostTextManager", $"Dispose failed: {ex.Message}");
            }
        }
    }
}
