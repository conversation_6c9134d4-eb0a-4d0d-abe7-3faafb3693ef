using System;
using System.IO;
using System.Text.Json;
using Microsoft.VisualStudio.Shell;
using AICodeAssistant.UI;

namespace AICodeAssistant.Services
{
    /// <summary>
    /// 设置管理器 - 统一管理应用程序设置的加载和保存
    /// </summary>
    public static class SettingsManager
    {
        private static readonly object _lockObject = new object();
        private static AppSettings _cachedSettings;
        private static DateTime _lastLoadTime = DateTime.MinValue;
        private static readonly TimeSpan CacheValidityPeriod = TimeSpan.FromMinutes(5);

        /// <summary>
        /// 加载应用程序设置
        /// </summary>
        /// <returns>应用程序设置对象</returns>
        public static AppSettings LoadSettings()
        {
            lock (_lockObject)
            {
                try
                {
                    // 检查缓存是否有效
                    if (_cachedSettings != null && 
                        DateTime.Now - _lastLoadTime < CacheValidityPeriod)
                    {
                        return _cachedSettings;
                    }

                    // 获取设置文件路径
                    var filePath = GetSettingsFilePath();
                    
                    if (File.Exists(filePath))
                    {
                        var json = File.ReadAllText(filePath);
                        _cachedSettings = JsonSerializer.Deserialize<AppSettings>(json) ?? AppSettings.CreateDefault();
                        ActivityLog.LogInformation("SettingsManager", $"设置已从文件加载: {filePath}");
                    }
                    else
                    {
                        _cachedSettings = AppSettings.CreateDefault();
                        ActivityLog.LogInformation("SettingsManager", "设置文件不存在，使用默认设置");
                    }

                    _lastLoadTime = DateTime.Now;
                    return _cachedSettings;
                }
                catch (Exception ex)
                {
                    ActivityLog.LogError("SettingsManager", $"加载设置失败: {ex.Message}");
                    _cachedSettings = AppSettings.CreateDefault();
                    _lastLoadTime = DateTime.Now;
                    return _cachedSettings;
                }
            }
        }

        /// <summary>
        /// 保存应用程序设置
        /// </summary>
        /// <param name="settings">要保存的设置对象</param>
        /// <returns>是否保存成功</returns>
        public static bool SaveSettings(AppSettings settings)
        {
            lock (_lockObject)
            {
                try
                {
                    if (settings == null)
                    {
                        ActivityLog.LogWarning("SettingsManager", "尝试保存空的设置对象");
                        return false;
                    }

                    // 获取设置文件路径
                    var filePath = GetSettingsFilePath();
                    var folderPath = Path.GetDirectoryName(filePath);

                    // 确保目录存在
                    if (!Directory.Exists(folderPath))
                    {
                        Directory.CreateDirectory(folderPath);
                        ActivityLog.LogInformation("SettingsManager", $"创建设置目录: {folderPath}");
                    }

                    // 序列化并保存设置
                    var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions 
                    { 
                        WriteIndented = true 
                    });
                    
                    File.WriteAllText(filePath, json);

                    // 更新缓存
                    _cachedSettings = settings;
                    _lastLoadTime = DateTime.Now;

                    ActivityLog.LogInformation("SettingsManager", $"设置已保存到: {filePath}");
                    return true;
                }
                catch (Exception ex)
                {
                    ActivityLog.LogError("SettingsManager", $"保存设置失败: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// 重置设置为默认值
        /// </summary>
        /// <returns>是否重置成功</returns>
        public static bool ResetToDefaults()
        {
            try
            {
                var defaultSettings = AppSettings.CreateDefault();
                var success = SaveSettings(defaultSettings);
                
                if (success)
                {
                    ActivityLog.LogInformation("SettingsManager", "设置已重置为默认值");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsManager", $"重置设置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清除设置缓存，强制下次加载时从文件读取
        /// </summary>
        public static void ClearCache()
        {
            lock (_lockObject)
            {
                _cachedSettings = null;
                _lastLoadTime = DateTime.MinValue;
                ActivityLog.LogInformation("SettingsManager", "设置缓存已清除");
            }
        }

        /// <summary>
        /// 检查设置文件是否存在
        /// </summary>
        /// <returns>设置文件是否存在</returns>
        public static bool SettingsFileExists()
        {
            try
            {
                var filePath = GetSettingsFilePath();
                return File.Exists(filePath);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsManager", $"检查设置文件存在性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取设置文件的完整路径
        /// </summary>
        /// <returns>设置文件路径</returns>
        public static string GetSettingsFilePath()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var folderPath = Path.Combine(appDataPath, Constants.Config.AppDataFolder);
                return Path.Combine(folderPath, Constants.Config.SettingsFileName);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsManager", $"获取设置文件路径失败: {ex.Message}");
                // 返回一个默认路径
                return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                  "AICodeAssistant", "settings.json");
            }
        }

        /// <summary>
        /// 获取设置目录路径
        /// </summary>
        /// <returns>设置目录路径</returns>
        public static string GetSettingsDirectoryPath()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                return Path.Combine(appDataPath, Constants.Config.AppDataFolder);
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsManager", $"获取设置目录路径失败: {ex.Message}");
                return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                                  "AICodeAssistant");
            }
        }

        /// <summary>
        /// 验证设置对象的有效性
        /// </summary>
        /// <param name="settings">要验证的设置对象</param>
        /// <returns>设置是否有效</returns>
        public static bool ValidateSettings(AppSettings settings)
        {
            try
            {
                if (settings == null)
                    return false;

                // 验证基本属性
                if (settings.OpenAI == null || settings.Ollama == null || 
                    settings.Completion == null || settings.Chat == null || 
                    settings.Security == null)
                    return false;

                // 验证数值范围
                if (settings.Completion.ContextLines < 1 || settings.Completion.ContextLines > 100)
                    return false;

                if (settings.Completion.MaxTokens < 1 || settings.Completion.MaxTokens > 4096)
                    return false;

                if (settings.Completion.Temperature < 0 || settings.Completion.Temperature > 2)
                    return false;

                if (settings.Chat.MaxContextLength < 1000 || settings.Chat.MaxContextLength > 1000000)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SettingsManager", $"验证设置失败: {ex.Message}");
                return false;
            }
        }
    }
}
