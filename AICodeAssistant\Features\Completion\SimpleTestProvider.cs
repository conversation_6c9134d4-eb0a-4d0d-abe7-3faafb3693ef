using System;
using System.ComponentModel.Composition;
using System.Windows.Media;
using Microsoft.VisualStudio.Text;
using Microsoft.VisualStudio.Text.Editor;
using Microsoft.VisualStudio.Text.Tagging;
using Microsoft.VisualStudio.Utilities;
using Microsoft.VisualStudio.Shell;

namespace AICodeAssistant.Features.Completion
{
    /// <summary>
    /// Simple Test Layer Definition
    /// </summary>
    internal static class SimpleTestLayerDefinitions
    {
        [Export(typeof(AdornmentLayerDefinition))]
        [Name("SimpleTestLayer")]
        [Order(After = PredefinedAdornmentLayers.Text)]
        internal static AdornmentLayerDefinition SimpleTestLayerDefinition;
    }

    // Disabled - using PureGhostTextProvider instead
    // [Export(typeof(IWpfTextViewCreationListener))]
    [ContentType("any")]
    [TextViewRole(PredefinedTextViewRoles.Document)]
    public class SimpleTestProvider : IWpfTextViewCreationListener
    {
        public void TextViewCreated(IWpfTextView textView)
        {
            try
            {
                ActivityLog.LogInformation("SimpleTestProvider", "*** SIMPLE TEST: TextViewCreated called ***");
                
                var manager = new SimpleTestManager(textView);
                textView.Properties.AddProperty(typeof(SimpleTestManager), manager);
                
                ActivityLog.LogInformation("SimpleTestProvider", "*** SIMPLE TEST: Manager created successfully ***");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SimpleTestProvider", $"*** SIMPLE TEST ERROR: {ex.Message} ***");
                ActivityLog.LogError("SimpleTestProvider", $"*** STACK: {ex.StackTrace} ***");
            }
        }
    }

    public class SimpleTestManager : IDisposable
    {
        private readonly IWpfTextView _textView;
        private readonly IAdornmentLayer _adornmentLayer;
        private bool _testDisplayed = false;

        public SimpleTestManager(IWpfTextView textView)
        {
            _textView = textView ?? throw new ArgumentNullException(nameof(textView));
            
            try
            {
                ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Initializing manager ***");
                
                // 获取装饰层
                _adornmentLayer = _textView.GetAdornmentLayer("SimpleTestLayer");
                ActivityLog.LogInformation("SimpleTestManager", $"*** SIMPLE TEST: Adornment layer: {_adornmentLayer != null} ***");

                if (_adornmentLayer == null)
                {
                    ActivityLog.LogError("SimpleTestManager", "*** SIMPLE TEST: Failed to get adornment layer ***");
                    return;
                }

                // 订阅事件
                _textView.LayoutChanged += OnLayoutChanged;
                _textView.Closed += OnClosed;

                ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Manager initialized successfully ***");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SimpleTestManager", $"*** SIMPLE TEST INIT ERROR: {ex.Message} ***");
                ActivityLog.LogError("SimpleTestManager", $"*** STACK: {ex.StackTrace} ***");
            }
        }

        private void OnLayoutChanged(object sender, TextViewLayoutChangedEventArgs e)
        {
            try
            {
                if (_testDisplayed)
                    return;

                ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Layout changed - attempting to show test text ***");
                
                ShowTestText();
                _testDisplayed = true;
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SimpleTestManager", $"*** SIMPLE TEST LAYOUT ERROR: {ex.Message} ***");
            }
        }

        private void ShowTestText()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Creating test text block ***");

                // 创建一个简单的文本块
                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = "*** SIMPLE TEST TEXT ***",
                    Foreground = new SolidColorBrush(Colors.Red),
                    Background = new SolidColorBrush(Colors.Yellow),
                    FontSize = 16,
                    FontWeight = System.Windows.FontWeights.Bold,
                    Margin = new System.Windows.Thickness(10)
                };

                ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Text block created ***");

                // 获取文本视图的第一行
                var firstLine = _textView.TextViewLines.FirstVisibleLine;
                if (firstLine == null)
                {
                    ActivityLog.LogError("SimpleTestManager", "*** SIMPLE TEST: No visible lines ***");
                    return;
                }

                var span = new SnapshotSpan(firstLine.Start, 0);
                
                ActivityLog.LogInformation("SimpleTestManager", $"*** SIMPLE TEST: Adding adornment at span {span} ***");

                // 添加到装饰层
                _adornmentLayer.AddAdornment(
                    AdornmentPositioningBehavior.TextRelative,
                    span,
                    "SimpleTest",
                    textBlock,
                    null);

                ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Adornment added successfully ***");
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SimpleTestManager", $"*** SIMPLE TEST SHOW ERROR: {ex.Message} ***");
                ActivityLog.LogError("SimpleTestManager", $"*** STACK: {ex.StackTrace} ***");
            }
        }

        private void OnClosed(object sender, EventArgs e)
        {
            ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Text view closed ***");
            Dispose();
        }

        public void Dispose()
        {
            try
            {
                ActivityLog.LogInformation("SimpleTestManager", "*** SIMPLE TEST: Disposing ***");

                if (_textView != null)
                {
                    _textView.LayoutChanged -= OnLayoutChanged;
                    _textView.Closed -= OnClosed;
                }

                _adornmentLayer?.RemoveAllAdornments();
            }
            catch (Exception ex)
            {
                ActivityLog.LogError("SimpleTestManager", $"*** SIMPLE TEST DISPOSE ERROR: {ex.Message} ***");
            }
        }
    }
}
